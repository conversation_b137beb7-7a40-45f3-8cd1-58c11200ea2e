<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>夸克自动转存</title>
  <!-- CSS -->
  <link rel="stylesheet" href="./static/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
  <link rel="stylesheet" href="./static/css/dashboard.css">
  <!-- Vue.js & Libs -->
  <script src="./static/js/jquery-3.5.1.slim.min.js"></script>
  <script src="./static/js/bootstrap.bundle.min.js"></script>
  <script src="./static/js/<EMAIL>"></script>
  <script src="./static/js/axios.min.js"></script>
  <script src="./static/js/v-jsoneditor.min.js"></script>
  <style>
    :root { --light-gray: #f8f9fa; --primary-light: #e7f1ff; }
    body { background-color: var(--light-gray); font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif; }
    main { padding-top: 24px; }
    .cursor-pointer { cursor: pointer; }
    .card { border: none; border-radius: .75rem; }
    .card-header { background-color: #fff; font-weight: 500; border-bottom: 1px solid #eef2f7; }
    .task-card .card-header { cursor: pointer; transition: background-color 0.2s ease-in-out; }
    .task-card .card-header:hover { background-color: #f8f9fa; }
    .task-status-indicator { width: 10px; height: 10px; border-radius: 50%; display: inline-block; vertical-align: middle; }
    .status-active { background-color: #198754; }
    .status-disabled { background-color: #6c757d; }
    .status-warning { background-color: #ffc107; }
    .list-anim-enter-active, .list-anim-leave-active { transition: all 0.4s ease; }
    .list-anim-enter, .list-anim-leave-to { opacity: 0; transform: translateY(20px); }
    .bottom-button { position: fixed; right: 25px; bottom: 25px; z-index: 1020; }
    .bottom-button .btn { width: 50px; height: 50px; border-radius: 50%; font-size: 20px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); }
    .custom-switch-lg .custom-control-label::before { height: 1.5rem; width: 2.75rem; border-radius: 0.75rem; }
    .custom-switch-lg .custom-control-label::after { width: calc(1.5rem - 4px); height: calc(1.5rem - 4px); border-radius: 0.75rem; }
    .custom-switch-lg .custom-control-input:checked~.custom-control-label::after { transform: translateX(1.25rem); }
    .nav-link.active { font-weight: bold; color: #0d6efd !important; background-color: var(--primary-light); border-radius: .5rem;}
    .sidebar .nav-link i { width: 24px; }
    .navbar-brand { font-weight: 600; }
    .save-indicator { position: absolute; top: 10px; right: 8px; width: 8px; height: 8px; background-color: #dc3545; border-radius: 50%; box-shadow: 0 0 0 0 rgba(220, 53, 69, 1); animation: pulse 2s infinite; }
    @keyframes pulse { 0% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); } 70% { transform: scale(1); box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); } 100% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); } }
  </style>
</head>

<body>
  <div id="app">
    <nav class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow-sm">
      <a class="navbar-brand col-md-3 col-lg-2 mr-0 px-3" href="#"><i class="bi bi-clouds-fill"></i> 夸克自动转存</a>
      <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-toggle="collapse" data-target="#sidebarMenu"><span class="navbar-toggler-icon"></span></button>
      
      <!-- Top Right Action Buttons -->
      <div class="d-flex align-items-center ml-auto pr-3">
        <button class="btn btn-outline-success btn-sm mr-2 position-relative" @click.prevent="saveConfig" data-toggle="tooltip" data-placement="bottom" title="保存配置 (Ctrl+S)">
          <i class="bi bi-save-fill"></i> 保存
          <span v-if="configModified" class="save-indicator"></span>
        </button>
        <button class="btn btn-outline-primary btn-sm" @click.prevent="runScriptNow()" data-toggle="tooltip" data-placement="bottom" title="运行所有任务 (Ctrl+R)">
          <i class="bi bi-play-circle-fill"></i> 运行
        </button>
      </div>
    </nav>

    <div class="container-fluid">
      <div class="row">
        <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-white sidebar collapse">
          <div class="sidebar-sticky pt-3 px-2">
            <ul class="nav flex-column">
              <li class="nav-item mb-1"><a class="nav-link" href="#" :class="{active: activeTab === 'dashboard'}" @click="changeTab('dashboard')"><i class="bi bi-grid-1x2-fill"></i> 仪表盘</a></li>
              <li class="nav-item mb-1"><a class="nav-link" href="#" :class="{active: activeTab === 'tasklist'}" @click="changeTab('tasklist')"><i class="bi bi-list-task"></i> 任务列表 <span class="badge badge-primary badge-pill ml-1" v-if="formData.tasklist.length">{{ formData.tasklist.length }}</span></a></li>
              <li class="nav-item mb-1"><a class="nav-link" href="#" :class="{active: activeTab === 'config'}" @click="changeTab('config')"><i class="bi bi-gear-fill"></i> 系统配置</a></li>
              <h6 class="sidebar-heading px-3 mt-4 mb-1 text-muted"><span>其他</span></h6>
              <li class="nav-item"><a class="nav-link" href="/logout"><i class="bi bi-box-arrow-right"></i> 退出</a></li>
            </ul>
            <div class="nav-bottom text-center"><p><a target="_blank" href="https://github.com/Cp0204/quark-auto-save/wiki"><i class="bi bi-book-half mr-1"></i>使用文档</a></p><p><a href="./static/js/qas.addtask.user.js"><i class="bi bi-cloud-plus-fill mr-1"></i>油猴脚本</a></p><p><a target="_blank" href="https://github.com/Cp0204/quark-auto-save"><i class="bi bi-github mr-1"></i>GitHub</a></p><p class="small text-muted" v-html="versionTips"></p></div>
          </div>
        </nav>

        <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-md-4">
          <form @submit.prevent="saveConfig" @keydown.enter.prevent>
            <!-- Dashboard -->
            <div v-if="activeTab === 'dashboard'">
                <div class="d-flex justify-content-between align-items-center pt-3 pb-2 mb-3 border-bottom"><h1 class="h2">仪表盘</h1></div>
                <div class="row">
                    <div class="col-md-6 col-xl-4 mb-4"><div class="card h-100 shadow-sm"><div class="card-header d-flex justify-content-between align-items-center"><span><i class="bi bi-hdd-stack-fill mr-2 text-primary"></i>系统状态</span><span class="badge badge-success">运行中</span></div><div class="card-body p-4"><ul class="list-group list-group-flush"><li class="list-group-item d-flex justify-content-between align-items-center px-0">定时规则 <span class="text-muted small font-weight-bold">{{ formData.crontab || '未设置' }}</span></li><li class="list-group-item d-flex justify-content-between align-items-center px-0">主账号(CK #1) <span class="badge badge-pill badge-light">未知</span></li></ul></div></div></div>
                    <div class="col-md-6 col-xl-4 mb-4"><div class="card h-100 shadow-sm"><div class="card-header d-flex justify-content-between align-items-center"><span><i class="bi bi-card-checklist mr-2 text-info"></i>任务概览</span><a href="#" @click.prevent="changeTab('tasklist')" class="small">查看全部</a></div><div class="card-body p-4"><ul class="list-group list-group-flush"><li class="list-group-item d-flex justify-content-between align-items-center px-0">总任务数 <span class="font-weight-bold">{{ formData.tasklist.length }}</span></li><li class="list-group-item d-flex justify-content-between align-items-center px-0 text-success">活动中任务 <span class="font-weight-bold">{{ activeTasksCount }}</span></li><li class="list-group-item d-flex justify-content-between align-items-center px-0 text-secondary">已禁用任务 <span class="font-weight-bold">{{ disabledTasksCount }}</span></li><li class="list-group-item d-flex justify-content-between align-items-center px-0 text-warning">有警告的任务 <span class="font-weight-bold">{{ warningTasksCount }}</span></li></ul></div></div></div>
                    <div class="col-md-12 col-xl-4 mb-4"><div class="card h-100 shadow-sm"><div class="card-header"><i class="bi bi-lightning-charge-fill mr-2 text-warning"></i>快捷操作</div><div class="card-body text-center d-flex flex-column justify-content-center p-4"><button type="button" class="btn btn-primary btn-lg mb-3" @click="changeTab('tasklist'); addTask();"><i class="bi bi-plus-circle-dotted"></i> 添加新任务</button><button type="button" class="btn btn-outline-secondary" @click="viewLogs()"><i class="bi bi-file-text"></i> 查看运行日志</button></div></div></div>
                </div>
            </div>

            <!-- Config -->
            <div v-if="activeTab === 'config'">
              <div class="d-flex justify-content-between align-items-center pt-3 pb-2 mb-3 border-bottom"><h1 class="h2">系统配置</h1></div>
              <div class="row">
                <div class="col-lg-6">
                  <div class="card shadow-sm mb-4"><div class="card-header d-flex justify-content-between align-items-center"><h5 class="mb-0"><i class="bi bi-cookie mr-2"></i>Cookie</h5><button type="button" class="btn btn-outline-primary btn-sm" @click="addCookie()"><i class="bi bi-plus-lg"></i> 添加</button></div><div class="card-body p-4"><p class="card-text text-muted small">1. 所有账号执行签到<br>2. 仅第一个账号执行转存。<b>强烈建议使用手机验证码<a target="_blank" href="https://pan.quark.cn/">登录</a>获取完整CK。</b></p><transition-group name="list-anim" tag="div"><div v-for="(value, index) in formData.cookie" :key="index" class="input-group mb-2"><div class="input-group-prepend"><span class="input-group-text">#{{ index + 1 }}</span></div><input type="text" v-model="formData.cookie[index]" class="form-control" placeholder="在此粘贴Cookie..."><div class="input-group-append"><button type="button" class="btn btn-outline-danger" @click="removeCookie(index)" title="删除"><i class="bi bi-trash"></i></button></div></div></transition-group><div v-if="!formData.cookie.length" class="alert alert-light text-center small p-2">暂无Cookie，点击右上角 "添加"</div></div></div>
                  <div class="card shadow-sm mb-4"><div class="card-header"><h5 class="mb-0"><i class="bi bi-clock-history mr-2"></i>定时规则</h5></div><div class="card-body p-4"><div class="input-group"><input type="text" v-model="formData.crontab" class="form-control" placeholder="例如: 0 */2 * * * (每2小时运行一次)"><div class="input-group-append"><a href="https://tool.lu/crontab/" target="_blank" class="btn btn-outline-secondary" title="CRON表达式在线生成与校验">?</a></div></div></div></div>
                  <div class="card shadow-sm mb-4"><div class="card-header d-flex justify-content-between align-items-center"><h5 class="mb-0"><i class="bi bi-slash-circle-fill mr-2"></i>文件黑名单</h5><button type="button" class="btn btn-outline-primary btn-sm" @click="addBlacklistItem()"><i class="bi bi-plus-lg"></i> 添加</button></div><div class="card-body p-4"><p class="card-text text-muted small">匹配的文件名将被跳过，不支持正则。</p><transition-group name="list-anim" tag="div"><div v-for="(item, index) in formData.file_blacklist" :key="index" class="input-group mb-2"><input type="text" v-model="formData.file_blacklist[index]" class="form-control" placeholder="输入要屏蔽的完整文件名"><div class="input-group-append"><button type="button" class="btn btn-outline-danger" @click="removeBlacklistItem(index)" title="删除"><i class="bi bi-trash"></i></button></div></div></transition-group><div v-if="!formData.file_blacklist || !formData.file_blacklist.length" class="alert alert-light text-center small p-2">暂无黑名单规则</div></div></div>
                </div>
                <div class="col-lg-6">
                  <div class="card shadow-sm mb-4"><div class="card-header d-flex justify-content-between align-items-center"><h5 class="mb-0"><i class="bi bi-bell-fill mr-2"></i>通知推送</h5><div><button type="button" class="btn btn-outline-info btn-sm mr-2" title="通知推送测试" @click="testPush()"><i class="bi bi-lightning-fill"></i> 测试</button><button type="button" class="btn btn-outline-primary btn-sm" @click="addPush()"><i class="bi bi-plus-lg"></i> 添加</button></div></div><div class="card-body p-4"><p class="card-text text-muted small">支持多个通知渠道，详情请查阅<a href="https://github.com/Cp0204/quark-auto-save/wiki/通知推送服务配置" target="_blank">文档</a>。</p><transition-group name="list-anim" tag="div"><div v-for="(value, key) in formData.push_config" :key="key" class="input-group mb-2"><div class="input-group-prepend"><span class="input-group-text" style="min-width: 120px;">{{ key }}</span></div><input type="text" v-model="formData.push_config[key]" class="form-control"><div class="input-group-append"><button type="button" class="btn btn-outline-danger" @click="removePush(key)"><i class="bi bi-trash"></i></button></div></div></transition-group><div v-if="!Object.keys(formData.push_config).length" class="alert alert-light text-center small p-2">暂无通知配置</div></div></div>
                  <div class="card shadow-sm mb-4"><div class="card-header"><h5 class="mb-0"><i class="bi bi-keyboard-fill mr-2"></i>快捷键设置</h5></div><div class="card-body p-4"><div class="d-flex justify-content-between align-items-center mb-3"><label for="saveShortcutSwitch" class="mb-0">保存配置 (Ctrl/Cmd + S)</label><div class="custom-control custom-switch custom-switch-lg"><input type="checkbox" class="custom-control-input" id="saveShortcutSwitch" v-model="formData.shortcuts.saveEnabled"><label class="custom-control-label" for="saveShortcutSwitch"></label></div></div><div class="d-flex justify-content-between align-items-center"><label for="runShortcutSwitch" class="mb-0">运行任务 (Ctrl/Cmd + R)</label><div class="custom-control custom-switch custom-switch-lg"><input type="checkbox" class="custom-control-input" id="runShortcutSwitch" v-model="formData.shortcuts.runEnabled"><label class="custom-control-label" for="runShortcutSwitch"></label></div></div></div></div>
                </div>
                <div class="col-12"><div class="card shadow-sm mb-4"><div class="card-header d-flex justify-content-between align-items-center"><h5 class="mb-0"><i class="bi bi-magic mr-2"></i>魔法匹配</h5><button type="button" class="btn btn-outline-primary btn-sm" @click="addMagicRegex()"><i class="bi bi-plus-lg"></i> 添加</button></div><div class="card-body p-4"><p class="card-text text-muted small">预定义正则匹配规则，在任务列表中可直接点击使用。</p><div v-for="(value, key) in formData.magic_regex" :key="key" class="form-group mb-2"><div class="input-group"><div class="input-group-prepend"><span class="input-group-text">魔法名</span></div><input type="text" :data-oldkey="key" :value="key" class="form-control" @change="updateMagicRegexKey($event.target.dataset.oldkey, $event.target.value)" placeholder="自定义名称"><div class="input-group-prepend"><span class="input-group-text">正则处理</span></div><input type="text" v-model="value.pattern" class="form-control" placeholder="匹配表达式"><input type="text" v-model="value.replace" class="form-control" placeholder="替换表达式"><div class="input-group-append"><button type="button" class="btn btn-outline-danger" @click="removeMagicRegex(key)"><i class="bi bi-trash"></i></button></div></div></div><div v-if="!Object.keys(formData.magic_regex).length" class="alert alert-light text-center small p-2">暂无魔法匹配规则</div></div></div></div>
              </div>
            </div>

            <!-- Task List -->
            <div v-if="activeTab === 'tasklist'">
              <div class="d-flex justify-content-between align-items-center pt-3 pb-2 mb-3 border-bottom"><h1 class="h2">任务列表</h1><button type="button" class="btn btn-primary" @click="addTask()"><i class="bi bi-plus-circle-fill mr-1"></i> 增加新任务</button></div>
              <div class="card mb-4 shadow-sm"><div class="card-body p-3"><div class="row align-items-center"><div class="col-md-5 col-lg-4 mb-2 mb-md-0"><div class="form-check form-check-inline mr-3"><input type="checkbox" class="form-check-input" id="selectAllCheckbox" @change="toggleSelectAllTasks" :checked="allTasksSelected" style="width: 1.2em; height: 1.2em;"><label for="selectAllCheckbox" class="form-check-label ml-2">全选</label></div><div class="btn-group"><button type="button" class="btn btn-outline-secondary dropdown-toggle btn-sm" data-toggle="dropdown" :disabled="selectedTasks.length === 0">批量操作 ({{ selectedTasks.length }})</button><div class="dropdown-menu dropdown-menu-right"><a class="dropdown-item" href="#" @click.prevent="bulkRunSelected()"><i class="bi bi-play-circle-fill text-primary"></i> 批量运行</a><div class="dropdown-divider"></div><a class="dropdown-item" href="#" @click.prevent="bulkToggleEnable(true)"><i class="bi bi-check-circle-fill text-success"></i> 批量启用</a><a class="dropdown-item" href="#" @click.prevent="bulkToggleEnable(false)"><i class="bi bi-slash-circle-fill text-secondary"></i> 批量禁用</a><div class="dropdown-divider"></div><a class="dropdown-item" href="#" @click.prevent="bulkDelete()"><i class="bi bi-trash-fill text-danger"></i> 批量删除</a></div></div></div><div class="col-md-7 col-lg-8"><div class="input-group"><div class="input-group-prepend"><span class="input-group-text"><i class="bi bi-search"></i></span></div><input type="text" class="form-control" v-model="taskNameFilter" placeholder="按名称筛选/搜索..."><select class="form-control" v-model="taskDirSelected"><option value="">所有路径</option><option v-for="(dir, index) in taskDirs" :key="index" :value="dir" v-html="dir"></option></select></div></div></div></div></div>
              <transition-group name="list-anim" tag="div">
                <div v-for="(task, index) in filteredTasks" :key="task.id" class="card task-card mb-3 shadow-sm">
                  <div class="card-header d-flex align-items-center" data-toggle="collapse" :data-target="'#collapse_'+task.id">
                      <input type="checkbox" class="form-check-input mr-3" :value="task.id" v-model="selectedTasks" @click.stop style="width: 1.2em; height: 1.2em;">
                      <span class="task-status-indicator" :class="getTaskStatusClass(task)" :title="getTaskStatusTitle(task)"></span>
                      <span class="font-weight-bold flex-grow-1 ml-2">#{{ formData.tasklist.indexOf(task) + 1 }}: {{ task.taskname || '未命名任务' }}</span>
                      <div class="task-actions">
                          <button class="btn btn-sm btn-outline-warning" v-if="task.shareurl_ban" :title="task.shareurl_ban" disabled @click.stop><i class="bi bi-exclamation-triangle-fill"></i></button>
                          <button type="button" class="btn btn-sm btn-outline-primary" @click.stop="runScriptNow([task.id])" title="运行此任务" v-else><i class="bi bi-play-fill"></i></button>
                          <button type="button" class="btn btn-sm btn-outline-danger ml-2" @click.stop="removeTask(task.id)" title="删除此任务"><i class="bi bi-trash3-fill"></i></button>
                      </div>
                  </div>
                  <div class="collapse" :id="'collapse_'+task.id"><div class="card-body">
                      <!-- Task Form Content (same as original) -->
                      <div class="alert alert-warning" role="alert" v-if="task.shareurl_ban" v-html="task.shareurl_ban"></div>
                      <div class="form-group row"><label class="col-sm-2 col-form-label">任务名称</label><div class="col-sm-10"><input type="text" class="form-control" v-model="task.taskname"></div></div>
                      <div class="form-group row"><label class="col-sm-2 col-form-label">分享链接</label><div class="col-sm-10"><input type="text" class="form-control" v-model="task.shareurl"></div></div>
                      <div class="form-group row"><label class="col-sm-2 col-form-label">保存路径</label><div class="col-sm-10"><input type="text" class="form-control" v-model="task.savepath"></div></div>
                      <div class="form-group row"><label class="col-sm-2 col-form-label">运行星期</label><div class="col-sm-10 col-form-label"><div class="form-check form-check-inline"><input class="form-check-input" type="checkbox" :checked="task.runweek.length === 7" @change="toggleAllWeekdays(task)" :indeterminate.prop="task.runweek.length > 0 && task.runweek.length < 7"><label class="form-check-label">全选</label></div><div class="form-check form-check-inline" v-for="(day, d_index) in weekdays" :key="d_index"><input class="form-check-input" type="checkbox" v-model="task.runweek" :value="d_index+1"><label class="form-check-label" v-html="day"></label></div></div></div>
                      <!-- More fields... -->
                  </div></div>
                </div>
              </transition-group>
              <div v-if="!filteredTasks.length" class="text-center text-muted p-5"><i class="bi bi-journal-x" style="font-size: 3rem;"></i><p class="mt-3">没有找到匹配的任务。<br>可以尝试调整筛选条件或<a href="#" @click.prevent="addTask()">创建新任务</a>。</p></div>
            </div>
          </form>

          <div class="bottom-button">
            <button type="button" class="btn btn-light" data-toggle="tooltip" data-placement="top" title="回到顶部" @click="scrollToX(0)"><i class="bi bi-arrow-up"></i></button>
          </div>
        </main>
      </div>
    </div>
    
    <!-- Modals (same as original) -->
    <div class="modal fade" tabindex="-1" id="logModal"><div class="modal-dialog modal-xl"><div class="modal-content"><div class="modal-header"><h5 class="modal-title"><i class="bi bi-body-text mr-2"></i><b>运行日志</b><div v-if="modalLoading" class="spinner-border spinner-border-sm ml-2" role="status"></div></h5><button type="button" class="close" data-dismiss="modal">×</button></div><div class="modal-body bg-dark text-white" style="max-height: 70vh; overflow-y: auto;"><pre v-html="run_log"></pre></div></div></div></div>
    <div class="modal fade" tabindex="-1" id="fileSelectModal"><div class="modal-dialog modal-lg"><div class="modal-content"><div class="modal-header"><h5 class="modal-title"><i class="bi bi-folder2-open mr-2"></i><b v-if="fileSelect.previewRegex">正则处理预览</b><b v-else-if="fileSelect.selectDir">选择{{fileSelect.selectShare ? '需转存的' : '保存到的'}}文件夹</b><b v-else>选择起始文件</b><div v-if="modalLoading" class="spinner-border spinner-border-sm m-1"></div></h5><button type="button" class="close" data-dismiss="modal">×</button></div><div class="modal-body small">...</div><div class="modal-footer">...</div></div></div></div>
  </div>

  <script>
    var app = new Vue({
      el: '#app',
      data: {
        version: "[[ version ]]",
        versionTips: "",
        formData: {
          cookie: [], crontab: "", push_config: {}, plugins: {}, tasklist: [], magic_regex: {}, file_blacklist: [], api_token: "",
          shortcuts: { saveEnabled: true, runEnabled: true }, // Updated shortcut structure
          source: { cloudsaver: { server: "", username: "", password: "", token: "" } },
        },
        activeTab: 'dashboard',
        taskNameFilter: "",
        taskDirSelected: "",
        selectedTasks: [],
        configModified: false,
        // ... other data properties from original code
      },
      computed: {
        activeTasksCount() { return this.formData.tasklist.filter(t => t.runweek && t.runweek.length > 0).length; },
        disabledTasksCount() { return this.formData.tasklist.filter(t => !t.runweek || t.runweek.length === 0).length; },
        warningTasksCount() { return this.formData.tasklist.filter(t => t.shareurl_ban).length; },
        filteredTasks() { return this.formData.tasklist.filter(t => t.taskname.toLowerCase().includes(this.taskNameFilter.toLowerCase()) && (this.taskDirSelected === "" || this.getParentDirectory(t.savepath) === this.taskDirSelected)); },
        allTasksSelected() { if (this.filteredTasks.length === 0) return false; return this.selectedTasks.length === this.filteredTasks.length; }
      },
      watch: {
        formData: { handler() { this.configModified = true; }, deep: true },
      },
      mounted() {
        this.fetchData();
        this.checkNewVersion();
        $(function () { $('[data-toggle="tooltip"]').tooltip() });
        document.addEventListener('keydown', this.handleKeyDown);
        // ... other mounted hooks
      },
      methods: {
        fetchData() {
          axios.get('/data').then(r => {
            let d = r.data.data;
            // Backward compatibility for shortcuts
            if (d.shortcutsEnabled !== undefined) {
              d.shortcuts = { saveEnabled: d.shortcutsEnabled, runEnabled: d.shortcutsEnabled };
              delete d.shortcutsEnabled;
            } else if (!d.shortcuts) {
              d.shortcuts = { saveEnabled: true, runEnabled: true };
            }
            d.tasklist.forEach((t, i) => { t.id = `task_${Date.now()}_${i}`; /* ... other init logic */ });
            // ... rest of original fetchData logic
            this.formData = d;
            setTimeout(() => { this.configModified = false; }, 100);
          });
        },
        handleKeyDown(event) {
          if (event.ctrlKey || event.metaKey) {
            if (event.key === 's' && this.formData.shortcuts.saveEnabled) {
              event.preventDefault(); this.saveConfig();
            } else if (event.key === 'r' && this.formData.shortcuts.runEnabled) {
              event.preventDefault(); this.runScriptNow();
            }
          }
        },
        runScriptNow(taskIds = null, test = false) {
          let body = {};
          if (test) { body = { "quark_test": true, "cookie": this.formData.cookie, "push_config": this.formData.push_config }; } 
          else if (taskIds && taskIds.length > 0) {
            let tasksToRun = this.formData.tasklist
              .filter(t => taskIds.includes(t.id))
              .map(t => { let task = { ...t }; delete task.id; delete task.runweek; return task; });
            if (tasksToRun.length === 0) return;
            body = { "tasklist": tasksToRun };
          } else if (this.configModified && !confirm('配置已修改但未保存，是否继续运行所有任务？')) return;
          
          $('#logModal').modal('show');
          // ... rest of original SSE logic
        },
        bulkRunSelected() {
          if (this.selectedTasks.length === 0) {
            alert("请先选择要运行的任务。");
            return;
          }
          if (confirm(`确定要立即运行选中的 ${this.selectedTasks.length} 个任务吗？`)) {
            this.runScriptNow(this.selectedTasks);
          }
        },
        // ... all other methods from original code, with id-based modifications
        removeTask(taskId) {
            const taskIndex = this.formData.tasklist.findIndex(t => t.id === taskId);
            if (taskIndex > -1 && confirm(`确认删除任务 [${this.formData.tasklist[taskIndex].taskname}] 吗？`)) {
                this.formData.tasklist.splice(taskIndex, 1);
            }
        },
        toggleSelectAllTasks(event) { this.selectedTasks = event.target.checked ? this.filteredTasks.map(t => t.id) : []; },
        bulkDelete() { if (this.selectedTasks.length > 0 && confirm(`确定要删除选中的 ${this.selectedTasks.length} 个任务吗？`)) { this.formData.tasklist = this.formData.tasklist.filter(t => !this.selectedTasks.includes(t.id)); this.selectedTasks = []; } },
        bulkToggleEnable(enable) { if(this.selectedTasks.length > 0) { this.formData.tasklist.forEach(t => { if (this.selectedTasks.includes(t.id)) t.runweek = enable ? [1,2,3,4,5,6,7] : []; }); this.selectedTasks = []; } },
        getTaskStatusClass(task) { if (task.shareurl_ban) return 'status-warning'; if (task.runweek && task.runweek.length > 0) return 'status-active'; return 'status-disabled'; },
        getTaskStatusTitle(task) { if (task.shareurl_ban) return `警告: ${task.shareurl_ban}`; if (task.runweek && task.runweek.length > 0) return '已启用'; return '已禁用'; },
        viewLogs() { $('#logModal').modal('show'); this.run_log="暂无日志，请先运行一次任务。"; },
        scrollToX(top = 0) { window.scrollTo({ top: top, behavior: "smooth" }); },
        // ... other original methods
      }
    });
  </script>
</body>
</html>