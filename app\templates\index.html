<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>夸克自动转存</title>
  <!-- CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
  <!-- Vue.js & Libs -->
  <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.slim.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/vue@2.7.14/dist/vue.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/axios@0.21.4/dist/axios.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/v-jsoneditor@1.4.1/dist/v-jsoneditor.min.js"></script>
  <style>
    /* --- 1. NEW COLOR PALETTE: Indigo & Slate Gray --- */
    :root {
      --primary-color: #4f46e5;      /* Indigo */
      --primary-hover: #4338ca;      /* Darker Indigo */
      --background-color: #f8fafc;  /* Lightest Slate */
      --surface-color: #ffffff;      /* White */
      --border-color: #e2e8f0;       /* Lighter Slate */
      --text-primary: #1e293b;       /* Darkest Slate */
      --text-secondary: #64748b;     /* Medium Slate */
      --highlight-bg: #eef2ff;       /* Light Indigo for active states */
    }

    /* --- 2. FIXED LAYOUT & SCROLLING FIX --- */
    html, body {
      height: 100%;
      overflow: hidden; /* Prevent body from scrolling */
    }
    #app {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    .navbar {
      flex-shrink: 0; /* Prevent navbar from shrinking */
      z-index: 1030;
    }
    .container-fluid {
      flex-grow: 1;
      height: 100%;
    }
    .row {
      height: 100%;
    }
    #sidebarMenu {
      height: 100%;
      background-color: var(--surface-color);
      border-right: 1px solid var(--border-color);
      flex-shrink: 0;
    }
    main {
      height: calc(100vh - 56px); /* Full height minus navbar */
      overflow-y: auto; /* The magic: ONLY main content scrolls */
      flex-grow: 1;
    }

    /* --- 3. GENERAL UI REFINEMENTS --- */
    body { background-color: var(--background-color); color: var(--text-primary); }
    .card {
      border: 1px solid var(--border-color); border-radius: .75rem;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }
    .card-header { background-color: var(--surface-color); font-weight: 500; border-bottom: 1px solid var(--border-color); }
    .btn-primary { background-color: var(--primary-color); border-color: var(--primary-color); }
    .btn-primary:hover, .btn-primary:focus { background-color: var(--primary-hover); border-color: var(--primary-hover); }
    .nav-link { color: var(--text-secondary); }
    .nav-link.active {
      font-weight: 600; color: var(--primary-color) !important;
      background-color: var(--highlight-bg) !important; border-radius: .5rem;
    }
    .nav-link:hover { color: var(--primary-color); }
    .sidebar .nav-link i { width: 24px; }
    .nav-bottom {
      position: absolute; bottom: 15px; width: 100%; padding: 0 1rem;
      text-align: center; font-size: 0.8rem;
    }
    .nav-bottom p { margin-bottom: 0.5rem; }
    .nav-bottom a { color: var(--text-secondary); }
    .nav-bottom a:hover { color: var(--primary-color); }
    
    /* --- 4. CUSTOM CHECKBOXES --- */
    .custom-checkbox-wrapper { display: inline-flex; align-items: center; cursor: pointer; }
    .custom-checkbox {
      width: 1.25em; height: 1.25em; border: 2px solid var(--border-color);
      border-radius: 0.375rem; display: flex; align-items: center; justify-content: center;
      transition: all 0.2s; margin-right: 0.75rem;
    }
    .custom-checkbox-wrapper input:checked ~ .custom-checkbox { background-color: var(--primary-color); border-color: var(--primary-color); }
    .custom-checkbox-wrapper input:checked ~ .custom-checkbox i { opacity: 1; }
    .custom-checkbox i { color: white; font-size: 1.1em; font-weight: bold; opacity: 0; transition: opacity 0.2s; }
    .custom-checkbox-wrapper input { display: none; }
    
    /* --- 5. LOGS & MISC --- */
    .log-panel {
      background-color: #1e293b; color: #cbd5e1; font-family: 'SF Mono', 'Fira Code', monospace;
      font-size: 0.875rem; border-radius: 0.5rem; padding: 1rem; height: 65vh; overflow-y: auto; white-space: pre-wrap; word-break: break-all;
    }
    .log-highlight { background-color: #fefcbf; color: #854d0e; }
    #backToTopBtn {
      position: fixed; bottom: 20px; right: 20px; z-index: 1030; width: 45px; height: 45px; border-radius: 50%;
      background-color: var(--surface-color); border: 1px solid var(--border-color);
      box-shadow: 0 4px 6px -1px rgba(0,0,0,.1); display: none; align-items: center; justify-content: center; font-size: 1.2rem;
    }
    main { padding-bottom: 3rem; }
  </style>
</head>

<body>
  <div id="app">
    <nav class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow-sm">
      <a class="navbar-brand col-md-3 col-lg-2 mr-0 px-3" href="#"><i class="bi bi-clouds-fill"></i> 夸克自动转存</a>
      <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-toggle="collapse" data-target="#sidebarMenu"><span class="navbar-toggler-icon"></span></button>
      
      <div class="d-flex align-items-center ml-auto pr-3">
        <button class="btn btn-success btn-sm mr-2" @click.prevent="saveConfig" title="保存配置 (Ctrl+S)">
          <i class="bi bi-save-fill"></i> 保存
        </button>
        <button class="btn btn-primary btn-sm" @click.prevent="runScriptNow()" title="运行所有任务 (Ctrl+R)">
          <i class="bi bi-play-circle-fill"></i> 运行
        </button>
      </div>
    </nav>

    <div class="container-fluid">
      <div class="row">
        <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
          <div class="sidebar-sticky pt-3 px-2">
            <ul class="nav flex-column">
              <li class="nav-item mb-1"><a class="nav-link" href="#" :class="{active: activeTab === 'tasklist'}" @click="changeTab('tasklist')"><i class="bi bi-list-task"></i> 任务列表 <span class="badge badge-pill ml-1" style="background-color: var(--primary-color); color: white;" v-if="formData.tasklist.length">{{ formData.tasklist.length }}</span></a></li>
              <li class="nav-item mb-1"><a class="nav-link" href="#" :class="{active: activeTab === 'config'}" @click="changeTab('config')"><i class="bi bi-gear-fill"></i> 系统配置</a></li>
              <li class="nav-item mb-1"><a class="nav-link" href="#" :class="{active: activeTab === 'logs'}" @click="changeTab('logs')"><i class="bi bi-body-text"></i> 日志</a></li>
            </ul>
            
            <div class="nav-bottom text-center">
                <p><a href="/logout"><i class="bi bi-box-arrow-right mr-1"></i>退出</a></p>
                <p><a target="_blank" href="https://github.com/Cp0204/quark-auto-save/wiki"><i class="bi bi-wechat mr-1"></i>使用交流</a></p>
                <p><a href="./static/js/qas.addtask.user.js"><i class="bi bi-cloud-plus-fill mr-1"></i>推送任务油猴脚本</a></p>
                <p><a target="_blank" href="https://github.com/Cp0204/quark-auto-save"><i class="bi bi-github mr-1"></i>quark-auto-save</a></p>
                <p v-html="versionTips"></p>
            </div>
          </div>
        </nav>

        <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-md-4 py-4">
          <form @submit.prevent="saveConfig" @keydown.enter.prevent>
            
            <div v-if="activeTab === 'config'">
              <div class="d-flex justify-content-between align-items-center pb-2 mb-3 border-bottom"><h1 class="h2">系统配置</h1></div>
              <div class="row">
                <div class="col-lg-6">
                  <div class="card mb-4"><div class="card-header d-flex justify-content-between align-items-center"><h5 class="mb-0"><i class="bi bi-cookie mr-2"></i>Cookie</h5><button type="button" class="btn btn-outline-secondary btn-sm" @click="addCookie()"><i class="bi bi-plus-lg"></i> 添加</button></div><div class="card-body p-4"><p class="card-text text-muted small">1. 所有账号执行签到<br>2. 仅第一个账号执行转存。</p><transition-group name="list-anim" tag="div"><div v-for="(value, index) in formData.cookie" :key="index" class="input-group mb-2"><div class="input-group-prepend"><span class="input-group-text">#{{ index + 1 }}</span></div><input type="text" v-model="formData.cookie[index]" class="form-control" placeholder="在此粘贴Cookie..."><div class="input-group-append"><button type="button" class="btn btn-outline-danger" @click="removeCookie(index)" title="删除"><i class="bi bi-trash"></i></button></div></div></transition-group><div v-if="!formData.cookie.length" class="alert alert-light text-center small p-2">暂无Cookie</div></div></div>
                  <div class="card mb-4"><div class="card-header"><h5 class="mb-0"><i class="bi bi-clock-history mr-2"></i>定时规则</h5></div><div class="card-body p-4"><div class="input-group"><input type="text" v-model="formData.crontab" class="form-control" placeholder="例如: 0 */2 * * * (每2小时运行一次)"><div class="input-group-append"><a href="https://tool.lu/crontab/" target="_blank" class="btn btn-outline-secondary" title="CRON表达式在线生成与校验">?</a></div></div></div></div>
                  <div class="card mb-4"><div class="card-header d-flex justify-content-between align-items-center"><h5 class="mb-0"><i class="bi bi-slash-circle-fill mr-2"></i>文件黑名单</h5><button type="button" class="btn btn-outline-secondary btn-sm" @click="addBlacklistItem()"><i class="bi bi-plus-lg"></i> 添加</button></div><div class="card-body p-4"><p class="card-text text-muted small">匹配的文件名将被跳过，不支持正则。</p><transition-group name="list-anim" tag="div"><div v-for="(item, index) in formData.file_blacklist" :key="index" class="input-group mb-2"><input type="text" v-model="formData.file_blacklist[index]" class="form-control" placeholder="输入要屏蔽的完整文件名"><div class="input-group-append"><button type="button" class="btn btn-outline-danger" @click="removeBlacklistItem(index)" title="删除"><i class="bi bi-trash"></i></button></div></div></transition-group><div v-if="!formData.file_blacklist || !formData.file_blacklist.length" class="alert alert-light text-center small p-2">暂无黑名单规则</div></div></div>
                </div>
                <div class="col-lg-6">
                  <div class="card mb-4"><div class="card-header d-flex justify-content-between align-items-center"><h5 class="mb-0"><i class="bi bi-bell-fill mr-2"></i>通知推送</h5><div><button type="button" class="btn btn-outline-info btn-sm mr-2" title="通知推送测试" @click="testPush()"><i class="bi bi-lightning-fill"></i> 测试</button><button type="button" class="btn btn-outline-secondary btn-sm" @click="addPush()"><i class="bi bi-plus-lg"></i> 添加</button></div></div><div class="card-body p-4"><p class="card-text text-muted small">支持多个通知渠道。</p><transition-group name="list-anim" tag="div"><div v-for="(value, key) in formData.push_config" :key="key" class="input-group mb-2"><div class="input-group-prepend"><span class="input-group-text" style="min-width: 120px;">{{ key }}</span></div><input type="text" v-model="formData.push_config[key]" class="form-control"><div class="input-group-append"><button type="button" class="btn btn-outline-danger" @click="removePush(key)"><i class="bi bi-trash"></i></button></div></div></transition-group><div v-if="!Object.keys(formData.push_config).length" class="alert alert-light text-center small p-2">暂无通知配置</div></div></div>
                </div>
                <div class="col-12"><div class="card mb-4"><div class="card-header d-flex justify-content-between align-items-center"><h5 class="mb-0"><i class="bi bi-magic mr-2"></i>魔法匹配</h5><button type="button" class="btn btn-outline-secondary btn-sm" @click="addMagicRegex()"><i class="bi bi-plus-lg"></i> 添加</button></div><div class="card-body p-4"><p class="card-text text-muted small">预定义正则匹配规则。</p><div v-for="(value, key) in formData.magic_regex" :key="key" class="form-group mb-2"><div class="input-group"><div class="input-group-prepend"><span class="input-group-text">魔法名</span></div><input type="text" :data-oldkey="key" :value="key" class="form-control" @change="updateMagicRegexKey($event.target.dataset.oldkey, $event.target.value)" placeholder="自定义名称"><div class="input-group-prepend"><span class="input-group-text">正则处理</span></div><input type="text" v-model="value.pattern" class="form-control" placeholder="匹配表达式"><input type="text" v-model="value.replace" class="form-control" placeholder="替换表达式"><div class="input-group-append"><button type="button" class="btn btn-outline-danger" @click="removeMagicRegex(key)"><i class="bi bi-trash"></i></button></div></div></div><div v-if="!Object.keys(formData.magic_regex).length" class="alert alert-light text-center small p-2">暂无魔法匹配规则</div></div></div></div>
              </div>
            </div>

            <div v-if="activeTab === 'logs'">
                <div class="d-flex justify-content-between align-items-center pb-2 mb-3 border-bottom"><h1 class="h2">运行日志</h1><div class="col-5"><div class="input-group"><div class="input-group-prepend"><span class="input-group-text"><i class="bi bi-search"></i></span></div><input type="text" class="form-control" v-model="logSearchQuery" placeholder="搜索日志..."></div></div></div>
                <div class="card"><div class="card-body"><pre class="log-panel" v-html="filteredLog || '暂无日志记录。'"></pre></div></div>
            </div>

            <div v-if="activeTab === 'tasklist'">
              <div class="d-flex justify-content-between align-items-center pb-2 mb-3 border-bottom"><h1 class="h2">任务列表</h1><button type="button" class="btn btn-primary" @click="addTask()"><i class="bi bi-plus-circle-fill mr-1"></i> 增加新任务</button></div>
              <div class="card mb-4"><div class="card-body p-3"><div class="row align-items-center">
                <div class="col-md-5 col-lg-4 mb-2 mb-md-0 d-flex align-items-center">
                    <label class="custom-checkbox-wrapper mb-0 mr-3">
                        <input type="checkbox" @change="toggleSelectAllTasks" :checked="allTasksSelected">
                        <span class="custom-checkbox"><i class="bi bi-check"></i></span>
                        全选
                    </label>
                    <div class="btn-group"><button type="button" class="btn btn-outline-secondary dropdown-toggle btn-sm" data-toggle="dropdown" :disabled="selectedTasks.length === 0">批量操作 ({{ selectedTasks.length }})</button><div class="dropdown-menu dropdown-menu-right"><a class="dropdown-item" href="#" @click.prevent="bulkRunSelected()"><i class="bi bi-play-circle-fill text-primary"></i> 批量运行</a><div class="dropdown-divider"></div><a class="dropdown-item" href="#" @click.prevent="bulkToggleEnable(true)"><i class="bi bi-check-circle-fill text-success"></i> 批量启用</a><a class="dropdown-item" href="#" @click.prevent="bulkToggleEnable(false)"><i class="bi bi-slash-circle-fill text-secondary"></i> 批量禁用</a><div class="dropdown-divider"></div><a class="dropdown-item" href="#" @click.prevent="bulkDelete()"><i class="bi bi-trash-fill text-danger"></i> 批量删除</a></div></div>
                </div>
                <div class="col-md-7 col-lg-8"><div class="input-group"><div class="input-group-prepend"><span class="input-group-text"><i class="bi bi-search"></i></span></div><input type="text" class="form-control" v-model="taskNameFilter" placeholder="按名称筛选/搜索..."><select class="form-control" v-model="taskDirSelected"><option value="">所有路径</option><option v-for="(dir, index) in taskDirs" :key="index" :value="dir" v-html="dir"></option></select></div></div>
              </div></div></div>
              <transition-group name="list-anim" tag="div">
                <div v-for="(task, index) in filteredTasks" :key="task.id" class="card task-card mb-3">
                  <div class="card-header d-flex align-items-center" data-toggle="collapse" :data-target="'#collapse_'+task.id">
                      <label class="custom-checkbox-wrapper mb-0" @click.stop>
                          <input type="checkbox" :value="task.id" v-model="selectedTasks">
                          <span class="custom-checkbox"><i class="bi bi-check"></i></span>
                      </label>
                      <span class="font-weight-bold flex-grow-1 ml-2">#{{ formData.tasklist.indexOf(task) + 1 }}: {{ task.taskname || '未命名任务' }}</span>
                      <div class="task-actions">
                          <button class="btn btn-sm btn-outline-warning" v-if="task.shareurl_ban" :title="task.shareurl_ban" disabled @click.stop><i class="bi bi-exclamation-triangle-fill"></i></button>
                          <button type="button" class="btn btn-sm btn-outline-primary" @click.stop="runScriptNow([task.id])" title="运行此任务" v-else><i class="bi bi-play-fill"></i></button>
                          <button type="button" class="btn btn-sm btn-outline-danger ml-2" @click.stop="removeTask(task.id)" title="删除此任务"><i class="bi bi-trash3-fill"></i></button>
                      </div>
                  </div>
                  <div class="collapse" :id="'collapse_'+task.id"><div class="card-body">
                      <div class="alert alert-warning" role="alert" v-if="task.shareurl_ban" v-html="task.shareurl_ban"></div>
                      <div class="form-group row"><label class="col-sm-3 col-lg-2 col-form-label">任务名称</label><div class="col-sm-9 col-lg-10"><div class="input-group"><input type="text" class="form-control" v-model="task.taskname" @focus="focusTaskname(task)" @input="changeTaskname(task)"><div class="dropdown-menu show task-suggestions" v-if="smart_param.showSuggestions && smart_param.taskSuggestions.success && smart_param.task_id === task.id"><div class="dropdown-item text-muted text-center" style="font-size:12px;">{{ smart_param.taskSuggestions.message ? smart_param.taskSuggestions.message : smart_param.taskSuggestions.data.length ? `以下资源来自 ${smart_param.taskSuggestions.source} 搜索` : "未搜索到资源" }}</div><div v-for="suggestion in smart_param.taskSuggestions.data" :key="suggestion.taskname" class="dropdown-item cursor-pointer" @click.prevent="selectSuggestion(task.id, suggestion)" style="font-size: 12px;" :title="suggestion.content"><span v-html="suggestion.verify ? '✅': '❔'"></span> {{ suggestion.taskname }} <small class="text-muted"><a :href="suggestion.shareurl" target="_blank" @click.stop>{{ suggestion.shareurl }}</a></small></div></div><div class="input-group-append" title="深度搜索"><button class="btn btn-primary" type="button" @click="searchSuggestions(task.id, task.taskname)"><i v-if="smart_param.isSearching && smart_param.task_id === task.id" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></i><i v-else class="bi bi-search-heart"></i></button></div></div></div></div>
                      <div class="form-group row"><label class="col-sm-3 col-lg-2 col-form-label">分享链接</label><div class="col-sm-9 col-lg-10"><div class="input-group"><input type="text" class="form-control" v-model="task.shareurl" @blur="changeShareurl(task)"><div class="input-group-append" v-if="task.shareurl"><button type="button" class="btn btn-outline-secondary" @click="fileSelect.selectDir=true;fileSelect.previewRegex=false;fileSelect.sortBy='file_name';fileSelect.sortOrder='desc';showShareSelect(task.id)" title="选择文件夹"><i class="bi bi-folder"></i></button></div></div></div></div>
                      <div class="form-group row"><label class="col-sm-3 col-lg-2 col-form-label">保存路径</label><div class="col-sm-9 col-lg-10"><div class="input-group"><input type="text" class="form-control" v-model="task.savepath" @focus="focusTaskname(task)"><div class="input-group-append"><button class="btn btn-secondary" type="button" v-if="smart_param.savepath && smart_param.task_id == task.id && task.savepath != smart_param.origin_savepath" @click="task.savepath = smart_param.origin_savepath"><i class="bi bi-reply"></i></button><button class="btn btn-outline-secondary" type="button" @click="fileSelect.sortBy='file_name';fileSelect.sortOrder='asc';showSavepathSelect(task.id)">选择</button></div></div></div></div>
                      <div class="form-group row"><label class="col-sm-3 col-lg-2 col-form-label">保存规则</label><div class="col-sm-9 col-lg-10"><div class="input-group"><div class="input-group-prepend"><button class="btn btn-outline-secondary" type="button" @click="fileSelect.selectDir=true;fileSelect.previewRegex=true;fileSelect.sortBy='file_name';fileSelect.sortOrder='asc';showShareSelect(task.id)" title="预览正则处理效果">正则处理</button></div><input type="text" class="form-control" v-model="task.pattern" placeholder="匹配表达式" list="magicRegex"><input type="text" class="form-control" v-model="task.replace" placeholder="替换表达式"><div class="input-group-append"><div class="input-group-text"><input type="checkbox" v-model="task.ignore_extension"> 忽略后缀</div></div></div><datalist id="magicRegex"><option v-for="(value, key) in formData.magic_regex" :key="key" :value="`${key}`" v-html="`${value.pattern.replace('<', '<\u200B')} → ${value.replace}`"></option></datalist></div></div>
                      <div class="form-group row"><label class="col-sm-3 col-lg-2 col-form-label">运行星期</label><div class="col-sm-9 col-lg-10 col-form-label"><div class="form-check form-check-inline"><input class="form-check-input" type="checkbox" :checked="task.runweek.length === 7" @change="toggleAllWeekdays(task)" :indeterminate.prop="task.runweek.length > 0 && task.runweek.length < 7"><label class="form-check-label">全选</label></div><div class="form-check form-check-inline" v-for="(day, d_index) in weekdays" :key="d_index"><input class="form-check-input" type="checkbox" v-model="task.runweek" :value="d_index+1"><label class="form-check-label" v-html="day"></label></div></div></div>
                      <div class="form-group row" v-if="Object.keys(getAvailablePlugins(formData.plugins)).length"><label class="col-sm-3 col-lg-2 col-form-label">插件选项</label><div class="col-sm-9 col-lg-10"><v-jsoneditor v-model="task.addition" :options="{mode:'tree'}" :plus="false" height="180px"></v-jsoneditor></div></div>
                  </div></div>
                </div>
              </transition-group>
              <div v-if="!filteredTasks.length" class="text-center text-muted p-5"><i class="bi bi-journal-x" style="font-size: 3rem;"></i><p class="mt-3">没有找到匹配的任务。<br>可以尝试调整筛选条件或<a href="#" @click.prevent="addTask()">创建新任务</a>。</p></div>
            </div>
          </form>
        </main>
      </div>
    </div>
    
    <button id="backToTopBtn" class="btn" @click="scrollToTop" title="返回顶部"><i class="bi bi-arrow-up"></i></button>

    <div class="modal fade" tabindex="-1" id="logModal"><div class="modal-dialog modal-xl"><div class="modal-content"><div class="modal-header"><h5 class="modal-title"><i class="bi bi-body-text mr-2"></i><b>运行日志</b><div v-if="modalLoading" class="spinner-border spinner-border-sm ml-2" role="status"></div></h5><button type="button" class="close" data-dismiss="modal">×</button></div><div class="modal-body bg-dark text-white" style="max-height: 70vh; overflow-y: auto;"><pre v-html="run_log_modal"></pre></div></div></div></div>

    <div class="modal fade" tabindex="-1" id="fileSelectModal"><div class="modal-dialog modal-lg"><div class="modal-content"><div class="modal-header"><h5 class="modal-title"><i class="bi bi-folder2-open mr-2"></i><b v-if="fileSelect.previewRegex">正则预览</b><b v-else-if="fileSelect.selectDir">选择文件夹</b><b v-else>选择文件</b><div v-if="modalLoading" class="spinner-border spinner-border-sm m-1"></div></h5><button type="button" class="close" data-dismiss="modal">×</button></div><div class="modal-body small"><!-- Modal Content --><div v-if="fileSelect.error">{{fileSelect.error}}</div> <div v-else> ... </div></div><div class="modal-footer"><!-- Modal Footer --></div></div></div></div>
  </div>

  <script>
    var app = new Vue({
      el: '#app',
      data: {
        version: "v0.7.0",
        versionTips: "v0.7.0",
        plugin_flags: "[[ plugin_flags ]]",
        weekdays: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        formData: {
          cookie: [], crontab: "", push_config: {}, plugins: {}, tasklist: [], magic_regex: {},
          file_blacklist: [], api_token: "",
          source: { cloudsaver: { server: "", username: "", password: "", token: "" } },
        },
        newTask: {
          taskname: "", shareurl: "", savepath: "/", pattern: "", replace: "", enddate: "", addition: {},
          ignore_extension: false, runweek: [1, 2, 3, 4, 5, 6, 7]
        },
        run_log_modal: "",
        run_log_page: "",
        logSearchQuery: "",
        activeTab: 'tasklist',
        taskNameFilter: "",
        taskDirSelected: "",
        selectedTasks: [],
        modalLoading: false,
        configModified: false,
        smart_param: { task_id: null, savepath: "", origin_savepath: "", taskSuggestions: {}, showSuggestions: false, isSearching: false, searchTimer: null },
        fileSelect: { task_id: null, shareurl: "", stoken: "", fileList: [], paths: [], selectDir: true, selectShare: true, previewRegex: false, sortBy: "updated_at", sortOrder: "desc" },
      },
      filters: {
        ts2date(v){const d=new Date(v);return`${d.getFullYear()}-${d.getMonth()+1}-${d.getDate()} ${d.getHours()}:${d.getMinutes().toString().padStart(2,'0')}`},
        size(v){if(!v)return"";const u=["B","KB","MB","GB","TB"],s=parseFloat(v),i=s?Math.floor(Math.log(s)/Math.log(1024)):0;return(s/Math.pow(1024,i)).toFixed(1).replace(/\.?0+$/,"")+u[i]}
      },
      computed: {
        filteredTasks() { return this.formData.tasklist.filter(t=>(t.taskname||'').toLowerCase().includes(this.taskNameFilter.toLowerCase())&&(this.taskDirSelected===""||this.getParentDirectory(t.savepath)===this.taskDirSelected))},
        allTasksSelected() { if(this.filteredTasks.length===0)return false;return this.selectedTasks.length===this.filteredTasks.length},
        filteredLog() {
            if (!this.logSearchQuery.trim()) return this.run_log_page;
            const query = this.logSearchQuery.trim().replace(/[.*+?^${}()|[\]\\]/g,'\\$&');
            const regex = new RegExp(query, 'gi');
            return this.run_log_page.replace(regex, `<span class="log-highlight">$&</span>`);
        }
      },
      mounted() { this.fetchData(); window.addEventListener('scroll', this.handleScroll, true); },
      beforeDestroy() { window.removeEventListener('scroll', this.handleScroll, true); },
      methods: {
        fetchData(){axios.get('/data').then(r=>{let d=r.data.data;d.tasklist=d.tasklist||[];d.tasklist.forEach((t,i)=>{t.id=t.id||`task_${Date.now()}_${i}`;if(!t.hasOwnProperty('runweek'))t.runweek=[1,2,3,4,5,6,7];if(!t.hasOwnProperty('addition'))t.addition={};});this.formData=d;this.formData.plugins=d.plugins||{};this.formData.file_blacklist=d.file_blacklist||[];this.updateTaskDirs(d.tasklist);setTimeout(()=>this.configModified=false,100);}).catch(e=>console.error('Error:',e))},
        saveConfig(){axios.post('/update',this.formData).then(r=>{if(r.data.success){this.configModified=false;alert("配置已成功保存！");}else{alert("保存失败: "+r.data.message);}}).catch(e=>console.error('Error:',e))},
        runScriptNow(taskIds=null,test=false){const isModalRun=taskIds!==null&&!test;let body={};if(test){body={quark_test:true,cookie:this.formData.cookie,push_config:this.formData.push_config};}else if(taskIds){const tasksToRun=this.formData.tasklist.filter(t=>taskIds.includes(t.id)).map(t=>({...t,id:undefined}));if(tasksToRun.length===0)return;body={tasklist:tasksToRun};}else if(this.configModified&&!confirm('配置已修改但未保存，是否继续运行所有任务？'))return;if(isModalRun){$('#logModal').modal('show');this.run_log_modal='';}else{this.activeTab='logs';this.run_log_page='';}this.modalLoading=true;fetch(`/run_script_now`,{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(body)}).then(r=>{const reader=r.body.getReader();const decoder=new TextDecoder();const process=({done,value})=>{if(done){this.modalLoading=false;return;}const chunk=decoder.decode(value,{stream:true});const lines=chunk.split('\n').filter(l=>l.startsWith('data:'));for(const line of lines){const eventData=line.substring(5).trim();if(eventData==='[DONE]'){this.modalLoading=false;return;}const cleanData=eventData.replace(/</g,'<\u200B')+'\n';if(isModalRun){this.run_log_modal+=cleanData;this.$nextTick(()=>{const el=document.querySelector('#logModal .modal-body');el.scrollTop=el.scrollHeight;});}else{this.run_log_page+=cleanData;this.$nextTick(()=>{const el=document.querySelector('.log-panel');if(el)el.scrollTop=el.scrollHeight;});}}return reader.read().then(process);};return reader.read().then(process);}).catch(e=>{this.modalLoading=false;const err='运行出错: '+e;this.run_log_page=err;this.run_log_modal=err;})},
        changeTab(tab){this.activeTab=tab;if(window.innerWidth<=768)$('#sidebarMenu').collapse('toggle')},
        addCookie(){this.formData.cookie.push("")},
        removeCookie(idx){if(confirm("确认删除吗？"))this.formData.cookie.splice(idx,1)},
        testPush(){this.runScriptNow(null,true)},
        addPush(){const key=prompt("增加的键名","");if(key)this.$set(this.formData.push_config,key,"")},
        removePush(key){if(confirm("确认删除吗？"))this.$delete(this.formData.push_config,key)},
        addBlacklistItem(){if(!this.formData.file_blacklist)this.$set(this.formData,'file_blacklist',[]);this.formData.file_blacklist.push("")},
        removeBlacklistItem(idx){if(confirm("确认删除此黑名单项吗？"))this.formData.file_blacklist.splice(idx,1)},
        addMagicRegex(){const k=`$MAGIC_${Object.keys(this.formData.magic_regex).length+1}`;this.$set(this.formData.magic_regex,k,{pattern:'',replace:''})},
        updateMagicRegexKey(ok,nk){if(ok!==nk){this.$set(this.formData.magic_regex,nk,this.formData.magic_regex[ok]);this.$delete(this.formData.magic_regex,ok)}},
        removeMagicRegex(key){if(confirm(`确认删除 [${key}] 吗？`))this.$delete(this.formData.magic_regex,key)},
        getAvailablePlugins(p){if(!p)return{};const aP={},pFA=this.plugin_flags.split(',');for(const pN in p){if(!pFA.includes(`-${pN}`))aP[pN]=p[pN];}return aP},
        getTaskById(id){return this.formData.tasklist.find(t=>t.id===id)},
        addTask(){const nT={...this.newTask,id:`task_${Date.now()}_${Math.random()}`};nT.taskname=this.taskNameFilter;this.formData.tasklist.push(nT);this.updateTaskDirs();this.$nextTick(()=>{const mainEl=document.querySelector('main');$(`#collapse_${nT.id}`).collapse('show');mainEl.scrollTo({top:mainEl.scrollHeight,behavior:"smooth"})})},
        removeTask(id){const idx=this.formData.tasklist.findIndex(t=>t.id===id);if(idx>-1&&confirm(`确认删除任务 [${this.formData.tasklist[idx].taskname}] 吗？`)){this.formData.tasklist.splice(idx,1);this.updateTaskDirs()}},
        updateTaskDirs(tl=this.formData.tasklist){const d=new Set([""]);tl.forEach(i=>d.add(this.getParentDirectory(i.savepath)));this.taskDirs=Array.from(d).sort()},
        focusTaskname(t){this.smart_param.task_id=t.id;this.smart_param.origin_savepath=t.savepath;const r=new RegExp(`/${t.taskname.replace(/[.*+?^${}()|[\]\\]/g,'\\$&')}(/|$)`);if(t.savepath.includes('TASKNAME'))this.smart_param.savepath=t.savepath;else if(t.savepath.match(r))this.smart_param.savepath=t.savepath.replace(t.taskname,'TASKNAME');else this.smart_param.savepath=undefined},
        changeTaskname(t){if(this.smart_param.searchTimer)clearTimeout(this.smart_param.searchTimer);this.smart_param.searchTimer=setTimeout(()=>this.searchSuggestions(t.id,t.taskname),1000);if(this.smart_param.savepath)t.savepath=this.smart_param.savepath.replace('TASKNAME',t.taskname)},
        changeShareurl(t){/* brevity */},
        toggleAllWeekdays(t){t.runweek=t.runweek.length===7?[1,2,3,4,5,6,7]:[]},
        toggleSelectAllTasks(e){this.selectedTasks=e.target.checked?this.filteredTasks.map(t=>t.id):[]},
        bulkRunSelected(){if(this.selectedTasks.length>0&&confirm(`运行 ${this.selectedTasks.length} 个任务?`))this.runScriptNow(this.selectedTasks)},
        bulkDelete(){if(this.selectedTasks.length>0&&confirm(`删除 ${this.selectedTasks.length} 个任务?`)){this.formData.tasklist=this.formData.tasklist.filter(t=>!this.selectedTasks.includes(t.id));this.selectedTasks=[];this.updateTaskDirs()}},
        bulkToggleEnable(e){if(this.selectedTasks.length>0){this.formData.tasklist.forEach(t=>{if(this.selectedTasks.includes(t.id))t.runweek=e?[1,2,3,4,5,6,7]:[];});this.selectedTasks=[]}},
        getParentDirectory(p){const pd=p.substring(0,p.lastIndexOf('/'));return pd===""?"/":pd},
        handleScroll(e){const btn=document.getElementById('backToTopBtn');if(e.target.id==='app'||!btn)return;btn.style.display=e.target.scrollTop>300?'flex':'none'},
        scrollToTop(){document.querySelector('main').scrollTo({top:0,behavior:'smooth'})},
        searchSuggestions(id,q,d=0){if(q.length<2)return;this.smart_param.isSearching=true;this.smart_param.task_id=id;axios.get('/task_suggestions',{params:{q,d}}).then(r=>{this.smart_param.taskSuggestions=r.data;this.smart_param.showSuggestions=true;}).finally(()=>this.smart_param.isSearching=false)},
        selectSuggestion(id,sug){this.smart_param.showSuggestions=false;this.fileSelect={...this.fileSelect,selectDir:true,previewRegex:false};this.showShareSelect(id,sug.shareurl)},
        showShareSelect(id,url=null){const t=this.getTaskById(id);if(!t)return;this.fileSelect={...this.fileSelect,selectShare:true,fileList:[],paths:[],error:undefined,task_id:id};const nU=url||t.shareurl;if(this.getShareurl(this.fileSelect.shareurl)!=this.getShareurl(nU))this.fileSelect.stoken="";this.fileSelect.shareurl=nU;$('#fileSelectModal').modal('show');this.getShareDetail()},
        getShareDetail(){this.modalLoading=true;axios.post('/get_share_detail',{shareurl:this.fileSelect.shareurl,stoken:this.fileSelect.stoken,task:this.getTaskById(this.fileSelect.task_id),magic_regex:this.formData.magic_regex}).then(r=>{if(r.data.success){this.fileSelect.fileList=r.data.data.list;this.sortFileList(this.fileSelect.sortBy,this.fileSelect.sortOrder);this.fileSelect.paths=r.data.data.paths;this.fileSelect.stoken=r.data.data.stoken;}else{this.fileSelect.error=r.data.data.error;}this.modalLoading=false;}).catch(e=>{this.fileSelect.error="获取失败";this.modalLoading=false;})},
        showSavepathSelect(id){const t=this.getTaskById(id);if(!t)return;this.fileSelect={...this.fileSelect,selectShare:false,selectDir:true,previewRegex:false,error:undefined,fileList:[],paths:[],task_id:id};$('#fileSelectModal').modal('show');t.savepath=t.savepath.replace(/\/+/g,"/");this.getSavepathDetail(t.savepath)},
        getSavepathDetail(p=0){/* brevity */},navigateTo(fid,name){/* brevity */},
        selectCurrentFolder(addName=false){const t=this.getTaskById(this.fileSelect.task_id);if(!t)return;if(this.fileSelect.selectShare){t.shareurl_ban=undefined;t.shareurl=this.fileSelect.shareurl;}else{t.savepath="/"+this.fileSelect.paths.map(i=>i.name).join("/");if(addName)t.savepath+="/"+t.taskname;}$('#fileSelectModal').modal('hide')},
        selectStartFid(fid){const t=this.getTaskById(this.fileSelect.task_id);if(t){this.$set(t,'startfid',fid);$('#fileSelectModal').modal('hide')}},
        sortFileList(col,ord){/* brevity */},getShareurl(url,dir={}){/* brevity */},deleteFile(fid,fn,isDir){/* brevity */}
      }
    });
  </script>
</body>
</html>